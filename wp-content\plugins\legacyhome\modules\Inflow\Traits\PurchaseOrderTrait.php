<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait PurchaseOrderTrait
{
	protected function getPurchaseOrderResource(): string
	{
		return 'purchase-orders';
	}

	/**
	 * Retrieve a list of purchase orders.
	 *
	 * @param array<string, mixed> $options Optional query filters, sorting, pagination.
	 * @return list<array<string, mixed>>|WP_Error
	 */
	public function listPurchaseOrders( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getPurchaseOrderResource(), null, $options );
	}

	/**
	 * Retrieve a single purchase order by ID.
	 *
	 * @param int|string $id Purchase order ID.
	 * @param array<string, mixed> $options Optional query parameters.
	 * @return array<string, mixed>|WP_Error
	 */
	public function getPurchaseOrder( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getPurchaseOrderResource(), $id, $options );
	}

	/**
	 * Create a new purchase order.
	 *
	 * @param array<string, mixed> $data Purchase order data to create.
	 * @return array<string, mixed>|WP_Error
	 */
	public function createPurchaseOrder( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getPurchaseOrderResource(), null, [], $data );
	}

	/**
	 * Update an existing purchase order.
	 *
	 * @param int|string $id Purchase order ID to update.
	 * @param array<string, mixed> $data New purchase order data.
	 * @return array<string, mixed>|WP_Error
	 */
	public function updatePurchaseOrder( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getPurchaseOrderResource(), $id, [], $data );
	}

	/**
	 * Delete a purchase order by ID.
	 *
	 * @param int|string $id Purchase order ID to delete.
	 * @return true|WP_Error True on success, or WP_Error on failure.
	 */
	public function deletePurchaseOrder( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getPurchaseOrderResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
