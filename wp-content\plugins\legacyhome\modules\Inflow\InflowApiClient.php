<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow;

use Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract;
use Contactjavas\Legacyhome\Module\Inflow\Traits\{
	VendorTrait,
	CustomerTrait,
	ProductTrait,
	SalesOrderTrait,
	PurchaseOrderTrait,
};
use WP_Error;

/**
 * Client for interacting with inFlow Inventory API.
 *
 * This class implements core request logic and uses traits for entity-specific endpoints.
 *
 * @implements InflowRequestContract
 */
final readonly class InflowApiClient implements InflowRequestContract
{
	use CustomerTrait;
	use VendorTrait;
	use ProductTrait;
	use SalesOrderTrait;
	use PurchaseOrderTrait;

	/**
	 * The base URL for inFlow API requests.
	 *
	 * @var string
	 */
	private string $baseUrl;

	/**
	 * HTTP headers including authentication for requests.
	 *
	 * @var array<string, string>
	 */
	private array $headers;

	/**
	 * Number of retry attempts on failure.
	 *
	 * @var int
	 */
	private int $maxRetries;

	/**
	 * Constructor for the inFlow API client.
	 *
	 * @param InflowAuthClient $auth The authentication client providing base URL and headers.
	 */
	public function __construct(
		private InflowAuthClient $auth
	) {
		$this->baseUrl    = rtrim( $auth->getBaseUrl(), '/' );
		$this->headers    = $auth->getHttpHeaders();
		$this->maxRetries = 2;
	}

	/**
	 * Sends an HTTP request to the inFlow API.
	 *
	 * Handles retry logic for server errors and parses the JSON response.
	 *
	 * @param string               $method   HTTP method (GET, POST, PUT, PATCH, DELETE).
	 * @param string               $resource API resource path (e.g. 'customers').
	 * @param int|string|null      $id       Optional ID appended to resource (e.g. 'customers/123').
	 * @param array<string, mixed> $query    Optional query parameters.
	 * @param array<string, mixed> $body     Optional request body (for write operations).
	 *
	 * @return list<array<string, mixed>>|array<string, mixed>|WP_Error Parsed JSON response as array or WP_Error on failure.
	 */
	public function request(
		string $method,
		string $resource,
		int|string|null $id = null,
		array $query = [],
		array $body = []
	): array|WP_Error {
		$method = strtoupper( $method );
		$url    = $this->baseUrl . '/' . ltrim( $resource, '/' );

		if ( $id !== null ) {
			$url .= '/' . urlencode( (string)$id );
		}

		if ( $method === 'GET' && $query ) {
			$url = add_query_arg( $query, $url );
		}

		$args = [
			'method'  => $method,
			'headers' => $this->headers,
			'timeout' => 15,
		];

		if ( in_array( $method, ['POST', 'PUT', 'PATCH', 'DELETE'], true ) && $body ) {
			$args['body'] = wp_json_encode( $body );
		}

		for ( $attempt = 1; $attempt <= $this->maxRetries; $attempt++ ) {
			$response = wp_remote_request( $url, $args );

			if ( is_wp_error( $response ) ) {
				if ( $attempt === $this->maxRetries ) {
					return $response;
				}
				sleep( pow( 2, $attempt ) );
				continue;
			}

			$code     = wp_remote_retrieve_response_code( $response );
			$bodyText = wp_remote_retrieve_body( $response );
			$parsed   = json_decode( $bodyText, true );

			if ( $code >= 500 ) {
				if ( $attempt === $this->maxRetries ) {
					return new WP_Error(
						'inflow_server_error',
						"Server error after {$attempt} attempts.",
						['status' => $code, 'body' => $parsed ?? $bodyText]
					);
				}
				sleep( pow( 2, $attempt ) );
				continue;
			}

			if ( $code >= 400 ) {
				return new WP_Error(
					'inflow_api_error',
					$parsed['message'] ?? "API error {$code}",
					['status' => $code, 'body' => $parsed ?? $bodyText]
				);
			}

			return is_array( $parsed ) ? $parsed : [];
		}

		return new WP_Error( 'inflow_unknown_error', 'Unknown failure during API request.' );
	}
}
