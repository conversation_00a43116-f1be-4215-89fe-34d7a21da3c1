<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait CustomerTrait
{
	protected function getCustomerResource(): string
	{
		return 'customers';
	}

	/**
	 * Retrieve a list of customers.
	 *
	 * @param array<string, mixed> $options Optional query filters, sorting, pagination.
	 * @return list<array<string, mixed>>|WP_Error
	 */
	public function listCustomers( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getCustomerResource(), null, $options );
	}

	/**
	 * Retrieve a single customer by ID.
	 *
	 * @param int|string $id Customer ID.
	 * @param array<string, mixed> $options Optional query parameters.
	 * @return array<string, mixed>|WP_Error
	 */
	public function getCustomer( int|string $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getCustomerResource(), $id, $options );
	}

	/**
	 * Create a new customer.
	 *
	 * @param array<string, mixed> $data Customer data to create.
	 * @return array<string, mixed>|WP_Error
	 */
	public function createCustomer( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getCustomerResource(), null, [], $data );
	}

	/**
	 * Update an existing customer.
	 *
	 * @param int|string $id Customer ID to update.
	 * @param array<string, mixed> $data New customer data.
	 * @return array<string, mixed>|WP_Error
	 */
	public function updateCustomer( int|string $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getCustomerResource(), $id, [], $data );
	}

	/**
	 * Delete a customer by ID.
	 *
	 * @param int|string $id Customer ID to delete.
	 * @return true|WP_Error True on success, or WP_Error on failure.
	 */
	public function deleteCustomer( int|string $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getCustomerResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
