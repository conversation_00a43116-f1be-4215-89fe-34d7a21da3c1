<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait SalesOrderTrait
{
	protected function getSalesOrderResource(): string
	{
		return 'sales-orders';
	}

	/**
	 * Retrieve a list of sales orders.
	 *
	 * @param array<string, mixed> $options Optional query filters, sorting, pagination.
	 * @return list<array<string, mixed>>|WP_Error
	 */
	public function listSalesOrders( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getSalesOrderResource(), null, $options );
	}

	/**
	 * Retrieve a single sales order by ID.
	 *
	 * @param int|string $id Sales order ID.
	 * @param array<string, mixed> $options Optional query parameters.
	 * @return array<string, mixed>|WP_Error
	 */
	public function getSalesOrder( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getSalesOrderResource(), $id, $options );
	}

	/**
	 * Create a new sales order.
	 *
	 * @param array<string, mixed> $data Sales order data to create.
	 * @return array<string, mixed>|WP_Error
	 */
	public function createSalesOrder( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getSalesOrderResource(), null, [], $data );
	}

	/**
	 * Update an existing sales order.
	 *
	 * @param int|string $id Sales order ID to update.
	 * @param array<string, mixed> $data New sales order data.
	 * @return array<string, mixed>|WP_Error
	 */
	public function updateSalesOrder( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getSalesOrderResource(), $id, [], $data );
	}

	/**
	 * Delete a sales order by ID.
	 *
	 * @param int|string $id Sales order ID to delete.
	 * @return true|WP_Error True on success, or WP_Error on failure.
	 */
	public function deleteSalesOrder( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getSalesOrderResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
