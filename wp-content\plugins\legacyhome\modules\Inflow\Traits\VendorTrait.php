<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait VendorTrait
{
	protected function getVendorResource(): string
	{
		return 'vendors';
	}

	/**
	 * Retrieve a list of vendors.
	 *
	 * @param array<string, mixed> $options Optional query filters, sorting, pagination.
	 * @return list<array<string, mixed>>|WP_Error
	 */
	public function listVendors( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getVendorResource(), null, $options );
	}

	/**
	 * Retrieve a single vendor by ID.
	 *
	 * @param int|string $id Vendor ID.
	 * @param array<string, mixed> $options Optional query parameters.
	 * @return array<string, mixed>|WP_Error
	 */
	public function getVendor( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getVendorResource(), $id, $options );
	}

	/**
	 * Create a new vendor.
	 *
	 * @param array<string, mixed> $data Vendor data to create.
	 * @return array<string, mixed>|WP_Error
	 */
	public function createVendor( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getVendorResource(), null, [], $data );
	}

	/**
	 * Update an existing vendor.
	 *
	 * @param int|string $id Vendor ID to update.
	 * @param array<string, mixed> $data New vendor data.
	 * @return array<string, mixed>|WP_Error
	 */
	public function updateVendor( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getVendorResource(), $id, [], $data );
	}

	/**
	 * Delete a vendor by ID.
	 *
	 * @param int|string $id Vendor ID to delete.
	 * @return true|WP_Error True on success, or WP_Error on failure.
	 */
	public function deleteVendor( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getVendorResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
