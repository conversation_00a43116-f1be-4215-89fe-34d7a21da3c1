<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\QuickBooks;

use WP_Error;
use Contactjavas\Legacyhome\Module\OAuth\OAuthClient;

// Add all traits here
use Contactjavas\Legacyhome\Module\QuickBooks\Traits\{
    CompanyTrait,
    CustomerTrait,
    InvoiceTrait,
    BillTrait,
    SalesReceiptTrait,
    VendorTrait,
    ReportTrait,
    UtilityTrait,
    AttachmentTrait,
    TaxTrait,
    TransactionTrait,
    RecurringTrait,
    TimeTrait,
    EmployeeTrait,
    AccountingTrait,
    ProjectTrait,
    PurchaseTrait
};

final readonly class QuickBooksApiClient
{
    use CompanyTrait,
        CustomerTrait,
        InvoiceTrait,
        BillTrait,
		SalesReceiptTrait,
        VendorTrait,
        ReportTrait,
        UtilityTrait,
        AttachmentTrait,
        TaxTrait,
        TransactionTrait,
        RecurringTrait,
        TimeTrait,
        EmployeeTrait,
        AccountingTrait,
        ProjectTrait,
        PurchaseTrait;

    private string $baseUri;

    public function __construct(
        private string $realmId,
        private OAuthClient $oauthClient
    ) {
		$this->baseUri = 'https://quickbooks.api.intuit.com/v3/company';
	}

    public function request(
        string $method,
        string $endpoint,
        array $query = [],
        array $body = []
    ): array|WP_Error {
        $accessToken = $this->oauthClient->getAccessToken();

        if ( !$accessToken ) {
            return new WP_Error( 'no_token', 'No valid access token available.' );
        }

        $url = $this->baseUri . '/' . $this->realmId . $endpoint;

        if ( !empty( $query ) ) {
            $url .= '?' . http_build_query( $query );
        }

        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
            ],
            'method'  => strtoupper( $method ),
        ];

        if ( !empty( $body ) ) {
            $args['body'] = json_encode( $body );
        }

        $response = wp_remote_request( $url, $args );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $code = wp_remote_retrieve_response_code( $response );
        $data = json_decode( wp_remote_retrieve_body( $response ), true );

        if ( $code >= 400 || !$data ) {
            return new WP_Error( 'api_error', 'QuickBooks API error', ['code' => $code, 'response' => $data] );
        }

        return $data;
    }
}
