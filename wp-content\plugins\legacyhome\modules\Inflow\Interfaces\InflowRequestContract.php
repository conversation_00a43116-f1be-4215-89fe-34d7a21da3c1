<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Interfaces;

use WP_Error;

interface InflowRequestContract
{
	/**
	 * Sends an HTTP request to the inFlow API.
	 *
	 * Handles retry logic for server errors and parses the JSON response.
	 *
	 * @param string               $method   HTTP method (GET, POST, PUT, PATCH, DELETE).
	 * @param string               $resource API resource path (e.g. 'customers').
	 * @param int|string|null      $id       Optional ID appended to resource (e.g. 'customers/123').
	 * @param array<string, mixed> $query    Optional query parameters.
	 * @param array<string, mixed> $body     Optional request body (for write operations).
	 *
	 * @return list<array<string, mixed>>|array<string, mixed>|WP_Error Parsed JSON response as array or WP_Error on failure.
	 */
	public function request(
		string $method,
		string $resource,
		int|string|null $id = null,
		array $query = [],
		array $body = []
	): array|WP_Error;
}
