<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait ProductTrait
{
	protected function getProductResource(): string
	{
		return 'products';
	}

	/**
	 * Retrieve a list of products.
	 *
	 * @param array<string, mixed> $options Optional query filters, sorting, pagination.
	 * @return list<array<string, mixed>>|WP_Error
	 */
	public function listProducts( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getProductResource(), null, $options );
	}

	/**
	 * Retrieve a single product by ID.
	 *
	 * @param int|string $id Product ID.
	 * @param array<string, mixed> $options Optional query parameters.
	 * @return array<string, mixed>|WP_Error
	 */
	public function getProduct( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getProductResource(), $id, $options );
	}

	/**
	 * Create a new product.
	 *
	 * @param array<string, mixed> $data Product data to create.
	 * @return array<string, mixed>|WP_Error
	 */
	public function createProduct( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getProductResource(), null, [], $data );
	}

	/**
	 * Update an existing product.
	 *
	 * @param int|string $id Product ID to update.
	 * @param array<string, mixed> $data New product data.
	 * @return array<string, mixed>|WP_Error
	 */
	public function updateProduct( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getProductResource(), $id, [], $data );
	}

	/**
	 * Delete a product by ID.
	 *
	 * @param int|string $id Product ID to delete.
	 * @return true|WP_Error True on success, or WP_Error on failure.
	 */
	public function deleteProduct( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getProductResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
